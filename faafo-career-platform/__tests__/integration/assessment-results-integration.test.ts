/**
 * Assessment Results Integration Tests
 * 
 * Integration tests for Assessment Results Integration covering end-to-end workflows and system interactions.
 * 
 * @category integration
 * @requires Database setup, service mocking
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { GET } from '@/app/api/assessment/results/[id]/route';
import { generateAssessmentInsights } from '@/lib/assessmentScoring';
import { getCareerPathSuggestions } from '@/lib/suggestionService';
import prisma from '@/lib/prisma';

// Mock NextRequest
class MockNextRequest {
  url: string;
  method: string;
  headers: Map<string, string>;

  constructor(url: string, options: any = {}) {
    this.url = url;
    this.method = options.method || 'GET';
    this.headers = new Map();
  }

  json() {
    return Promise.resolve({});
  }
}

// Mock the auth
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(() => Promise.resolve({
    user: { id: 'test-user-id', email: '<EMAIL>' }
  }))
}));

// Mock the auth options
jest.mock('@/lib/auth', () => ({
  authOptions: {}
}));

describe('Assessment Results Integration', () => {
  let testUserId: string;
  let testAssessmentId: string;
  let mockPrisma: any;

  beforeEach(async () => {
    // Clear all mocks
    jest.clearAllMocks();

    // Get the global mock Prisma
    mockPrisma = (global as any).mockPrisma;

    // Setup test data
    testUserId = 'test-user-id';
    testAssessmentId = 'test-assessment-id';

    const testUser = {
      id: testUserId,
      email: '<EMAIL>',
      name: 'Test User',
      emailVerified: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Mock user creation
    mockPrisma.user.create.mockResolvedValue(testUser);

    const testResponses = [
      {
        id: 'response-1',
        assessmentId: testAssessmentId,
        questionKey: 'dissatisfaction_triggers',
        answerValue: ['lack_of_growth', 'compensation'],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'response-2',
        assessmentId: testAssessmentId,
        questionKey: 'financial_comfort',
        answerValue: 4,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'response-3',
        assessmentId: testAssessmentId,
        questionKey: 'confidence_level',
        answerValue: 3,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'response-4',
        assessmentId: testAssessmentId,
        questionKey: 'support_system',
        answerValue: 4,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'response-5',
        assessmentId: testAssessmentId,
        questionKey: 'risk_tolerance',
        answerValue: 3,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'response-6',
        assessmentId: testAssessmentId,
        questionKey: 'top_skills',
        answerValue: ['technical_programming', 'problem_solving', 'communication'],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'response-7',
        assessmentId: testAssessmentId,
        questionKey: 'career_change_motivation',
        answerValue: 'better_work_life_balance',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'response-8',
        assessmentId: testAssessmentId,
        questionKey: 'biggest_obstacles',
        answerValue: ['financial_concerns', 'lack_of_experience'],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'response-9',
        assessmentId: testAssessmentId,
        questionKey: 'transition_timeline',
        answerValue: 'medium_term',
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    ];

    const testAssessment = {
      id: testAssessmentId,
      userId: testUserId,
      status: 'COMPLETED',
      currentStep: 5,
      completedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      responses: testResponses, // Include responses in the assessment
    };

    // Mock assessment creation and queries
    mockPrisma.assessment.create.mockResolvedValue(testAssessment);
    mockPrisma.assessment.findUnique.mockResolvedValue(testAssessment);

    // Mock assessment responses
    mockPrisma.assessmentResponse.createMany.mockResolvedValue({ count: testResponses.length });
    mockPrisma.assessmentResponse.findMany.mockResolvedValue(testResponses);

    const testCareerPath = {
      id: 'career-path-1',
      name: 'Software Developer',
      slug: 'software-developer',
      overview: 'Build software applications and systems',
      pros: JSON.stringify(['High demand', 'Good salary', 'Remote work options']),
      cons: JSON.stringify(['Long hours', 'Constant learning required']),
      actionableSteps: [
        { title: 'Learn programming languages', description: 'Start with Python or JavaScript' },
        { title: 'Build portfolio projects', description: 'Create 3-5 projects to showcase skills' }
      ],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const testSuggestionRule = {
      id: 'suggestion-rule-1',
      careerPathId: testCareerPath.id,
      questionKey: 'top_skills',
      answerValue: 'technical_programming',
      weight: 5,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Mock career path and suggestion rule operations
    mockPrisma.careerPath.create.mockResolvedValue(testCareerPath);
    mockPrisma.careerPath.findMany.mockResolvedValue([testCareerPath]);
    mockPrisma.suggestionRule.create.mockResolvedValue(testSuggestionRule);
    mockPrisma.suggestionRule.findMany.mockResolvedValue([testSuggestionRule]);

    // Mock additional API route dependencies
    mockPrisma.userSkillProgress.findMany.mockResolvedValue([]);
    mockPrisma.learningResource.findMany.mockResolvedValue([]);
  });

  describe('Assessment Results API', () => {
    it('should return comprehensive assessment results', async () => {
      const request = new MockNextRequest(`http://localhost/api/assessment/results/${testAssessmentId}`) as any;
      const response = await GET(request, { params: { id: testAssessmentId } });
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      
      const results = data.data;
      
      // Check assessment data
      expect(results.assessment.id).toBe(testAssessmentId);
      expect(results.assessment.status).toBe('COMPLETED');
      
      // Check insights
      expect(results.insights).toBeDefined();
      expect(results.insights.scores).toBeDefined();
      expect(results.insights.scores.readinessScore).toBeGreaterThan(0);
      expect(results.insights.topSkills).toContain('technical_programming');
      expect(results.insights.primaryMotivation).toBe('better_work_life_balance');
      
      // Check career suggestions
      expect(results.careerSuggestions).toBeDefined();
      expect(Array.isArray(results.careerSuggestions)).toBe(true);
      
      if (results.careerSuggestions.length > 0) {
        const suggestion = results.careerSuggestions[0];
        expect(suggestion.careerPath).toBeDefined();
        expect(suggestion.score).toBeGreaterThan(0);
        expect(suggestion.matchReason).toBeDefined();
      }
    });

    it('should return 404 for non-existent assessment', async () => {
      // Mock non-existent assessment
      mockPrisma.assessment.findUnique.mockResolvedValueOnce(null);

      const request = new MockNextRequest('http://localhost/api/assessment/results/non-existent-id') as any;
      const response = await GET(request, { params: { id: 'non-existent-id' } });
      
      expect(response.status).toBe(404);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Assessment not found');
    });

    it('should return 400 for incomplete assessment', async () => {
      const incompleteAssessmentId = 'incomplete-assessment-id';
      const incompleteAssessment = {
        id: incompleteAssessmentId,
        userId: testUserId,
        status: 'IN_PROGRESS',
        currentStep: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the incomplete assessment
      mockPrisma.assessment.findUnique.mockResolvedValueOnce(incompleteAssessment);

      const request = new MockNextRequest(`http://localhost/api/assessment/results/${incompleteAssessmentId}`) as any;
      const response = await GET(request, { params: { id: incompleteAssessmentId } });

      expect(response.status).toBe(400);

      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Assessment not completed');
    });
  });

  describe('Assessment Insights Generation', () => {
    it('should generate correct insights from assessment responses', async () => {
      const responses = {
        financial_comfort: 4,
        confidence_level: 3,
        support_system: 4,
        risk_tolerance: 3,
        top_skills: ['technical_programming', 'problem_solving'],
        career_change_motivation: 'better_work_life_balance',
        biggest_obstacles: ['financial_concerns'],
        transition_timeline: 'medium_term'
      };

      const insights = await generateAssessmentInsights(responses);
      
      expect(insights.scores.readinessScore).toBeGreaterThan(0);
      expect(insights.scores.readinessScore).toBeLessThanOrEqual(100);
      expect(insights.scores.financialReadiness).toBe(4);
      expect(insights.scores.riskTolerance).toBe(3);
      expect(insights.topSkills).toContain('technical_programming');
      expect(insights.primaryMotivation).toBe('better_work_life_balance');
      expect(insights.biggestObstacles).toContain('financial_concerns');
      expect(insights.recommendedTimeline).toBeDefined();
      expect(insights.keyRecommendations.length).toBeGreaterThan(0);
    });
  });

  describe('Career Path Suggestions', () => {
    it('should generate career suggestions based on assessment', async () => {
      const suggestions = await getCareerPathSuggestions(testAssessmentId);
      
      expect(Array.isArray(suggestions)).toBe(true);
      
      if (suggestions.length > 0) {
        const suggestion = suggestions[0];
        expect(suggestion.careerPath).toBeDefined();
        expect(suggestion.careerPath.name).toBeDefined();
        expect(suggestion.score).toBeGreaterThan(0);
        expect(suggestion.matchReason).toBeDefined();
        expect(suggestion.skillAlignment).toBeGreaterThanOrEqual(0);
      }
    });

    it('should return suggestions sorted by score', async () => {
      const suggestions = await getCareerPathSuggestions(testAssessmentId);
      
      if (suggestions.length > 1) {
        for (let i = 0; i < suggestions.length - 1; i++) {
          expect(suggestions[i].score).toBeGreaterThanOrEqual(suggestions[i + 1].score);
        }
      }
    });
  });
});
