/**
 * Email Verification Flow Tests
 * 
 * Integration tests for Email Verification Flow covering end-to-end workflows and system interactions.
 * 
 * @category integration
 * @requires Database setup, service mocking
 */

import { NextRequest } from 'next/server';
import { POST as signupPOST } from '@/app/api/signup/route';
import { POST as verifyPOST, GET as verifyGET } from '@/app/api/auth/verify-email/route';
import { POST as resendPOST } from '@/app/api/auth/resend-verification/route';
import prisma from '@/lib/prisma';
import { sendEmail } from '@/lib/email';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  user: {
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  verificationToken: {
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
  $transaction: jest.fn(),
}));

jest.mock('@/lib/email', () => ({
  sendEmail: jest.fn(),
}));

jest.mock('bcryptjs', () => ({
  hash: jest.fn(),
}));

jest.mock('uuid', () => ({
  v4: jest.fn(),
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockSendEmail = sendEmail as jest.MockedFunction<typeof sendEmail>;
const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;
const mockUuidv4 = uuidv4 as jest.MockedFunction<typeof uuidv4>;

// Type-safe mock helpers
const mockUserFindUnique = mockPrisma.user.findUnique as jest.MockedFunction<any>;
const mockUserCreate = mockPrisma.user.create as jest.MockedFunction<any>;
const mockUserUpdate = mockPrisma.user.update as jest.MockedFunction<any>;
const mockVerificationTokenFindUnique = mockPrisma.verificationToken.findUnique as jest.MockedFunction<any>;
const mockVerificationTokenFindFirst = mockPrisma.verificationToken.findFirst as jest.MockedFunction<any>;
const mockVerificationTokenCreate = mockPrisma.verificationToken.create as jest.MockedFunction<any>;
const mockVerificationTokenDelete = mockPrisma.verificationToken.delete as jest.MockedFunction<any>;
const mockVerificationTokenDeleteMany = mockPrisma.verificationToken.deleteMany as jest.MockedFunction<any>;
const mockBcryptHash = mockBcrypt.hash as jest.MockedFunction<any>;

describe('Email Verification Integration Flow', () => {
  const testEmail = '<EMAIL>';
  const testPassword = 'password123';
  const hashedPassword = 'hashed_password_123';
  const verificationToken = 'integration-test-token';
  const userId = 'integration-user-123';

  beforeEach(() => {
    jest.clearAllMocks();
    process.env.NEXTAUTH_URL = 'http://localhost:3000';
    
    // Setup common mocks
    (mockBcryptHash as any).mockResolvedValue(hashedPassword);
    mockUuidv4.mockReturnValue(verificationToken);
    mockSendEmail.mockResolvedValue({ success: true, data: { id: 'email123' } });
  });

  describe('Complete Email Verification Flow', () => {
    it('should complete the full signup and verification flow', async () => {
      // Step 1: User signs up
      mockUserFindUnique.mockResolvedValue(null); // User doesn't exist
      
      const newUser = {
        id: userId,
        email: testEmail,
        password: hashedPassword,
        emailVerified: null,
        name: null,
        passwordResetToken: null,
        passwordResetExpires: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
      };

      mockUserCreate.mockResolvedValue(newUser);
      mockVerificationTokenCreate.mockResolvedValue({
        identifier: testEmail,
        token: verificationToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      const signupRequest = new NextRequest('http://localhost:3000/api/signup', {
        method: 'POST',
        body: JSON.stringify({ email: testEmail, password: testPassword }),
        headers: { 'Content-Type': 'application/json' },
      });

      const signupResponse = await signupPOST(signupRequest);
      const signupData = await signupResponse.json();

      expect(signupResponse.status).toBe(201);
      expect(signupData.requiresVerification).toBe(true);
      expect(mockSendEmail).toHaveBeenCalled();

      // Step 2: User clicks verification link (GET request to check token)
      mockVerificationTokenFindUnique.mockResolvedValue({
        identifier: testEmail,
        token: verificationToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      mockUserFindUnique.mockResolvedValue(newUser);

      const verifyGetUrl = new URL(`http://localhost:3000/api/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(testEmail)}`);
      const verifyGetRequest = new NextRequest(verifyGetUrl);

      const verifyGetResponse = await verifyGET(verifyGetRequest);
      const verifyGetData = await verifyGetResponse.json();

      expect(verifyGetResponse.status).toBe(200);
      expect(verifyGetData.valid).toBe(true);
      expect(verifyGetData.alreadyVerified).toBe(false);

      // Step 3: User submits verification (POST request)
      const verifiedUser = { ...newUser, emailVerified: new Date() };
      
      mockPrisma.$transaction.mockResolvedValue([
        verifiedUser,
        {}, // Token deletion result
      ]);

      const verifyPostRequest = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token: verificationToken, email: testEmail }),
        headers: { 'Content-Type': 'application/json' },
      });

      const verifyPostResponse = await verifyPOST(verifyPostRequest);
      const verifyPostData = await verifyPostResponse.json();

      expect(verifyPostResponse.status).toBe(200);
      expect(verifyPostData.message).toBe('Email verified successfully.');

      // Verify the transaction was called to update user and delete token
      expect(mockPrisma.$transaction).toHaveBeenCalled();
    });

    it('should handle resend verification during the flow', async () => {
      // Setup: User exists but is unverified
      const unverifiedUser = {
        id: userId,
        email: testEmail,
        password: hashedPassword,
        emailVerified: null,
        name: 'Test User',
        passwordResetToken: null,
        passwordResetExpires: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
      };

      mockUserFindUnique.mockResolvedValue(unverifiedUser);
      mockVerificationTokenFindFirst.mockResolvedValue(null); // No recent tokens
      mockVerificationTokenDeleteMany.mockResolvedValue({ count: 0 });
      mockVerificationTokenCreate.mockResolvedValue({
        identifier: testEmail,
        token: verificationToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      const resendRequest = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
        method: 'POST',
        body: JSON.stringify({ email: testEmail }),
        headers: { 'Content-Type': 'application/json' },
      });

      const resendResponse = await resendPOST(resendRequest);
      const resendData = await resendResponse.json();

      expect(resendResponse.status).toBe(200);
      expect(resendData.message).toBe('Verification email sent successfully.');
      expect(mockSendEmail).toHaveBeenCalledWith({
        to: testEmail,
        subject: 'Verify your email for FAAFO Career Platform',
        template: expect.any(Object),
      });
    });

    it('should handle expired token scenario', async () => {
      // User tries to verify with expired token
      const expiredToken = {
        identifier: testEmail,
        token: verificationToken,
        expires: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
      };

      mockPrisma.verificationToken.findUnique.mockResolvedValue(expiredToken);
      mockPrisma.verificationToken.delete.mockResolvedValue(expiredToken);

      const verifyRequest = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token: verificationToken, email: testEmail }),
        headers: { 'Content-Type': 'application/json' },
      });

      const verifyResponse = await verifyPOST(verifyRequest);
      const verifyData = await verifyResponse.json();

      expect(verifyResponse.status).toBe(400);
      expect(verifyData.error).toBe('Verification token has expired.');
      expect(mockPrisma.verificationToken.delete).toHaveBeenCalledWith({
        where: { token: verificationToken },
      });

      // User can then request a new verification email
      const unverifiedUser = {
        id: userId,
        email: testEmail,
        password: hashedPassword,
        emailVerified: null,
        name: 'Test User',
        passwordResetToken: null,
        passwordResetExpires: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
      };

      const newToken = 'new-verification-token';
      mockUuidv4.mockReturnValue(newToken);
      mockPrisma.user.findUnique.mockResolvedValue(unverifiedUser);
      mockPrisma.verificationToken.findFirst.mockResolvedValue(null);
      mockPrisma.verificationToken.deleteMany.mockResolvedValue({ count: 1 });
      mockPrisma.verificationToken.create.mockResolvedValue({
        identifier: testEmail,
        token: newToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      const resendRequest = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
        method: 'POST',
        body: JSON.stringify({ email: testEmail }),
        headers: { 'Content-Type': 'application/json' },
      });

      const resendResponse = await resendPOST(resendRequest);
      expect(resendResponse.status).toBe(200);
    });

    it('should handle already verified user gracefully', async () => {
      // User tries to verify again after already being verified
      const verifiedUser = {
        id: userId,
        email: testEmail,
        password: hashedPassword,
        emailVerified: new Date(),
        name: 'Test User',
        passwordResetToken: null,
        passwordResetExpires: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
      };

      mockPrisma.verificationToken.findUnique.mockResolvedValue({
        identifier: testEmail,
        token: verificationToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      mockPrisma.user.findUnique.mockResolvedValue(verifiedUser);
      mockPrisma.verificationToken.delete.mockResolvedValue({
        identifier: testEmail,
        token: verificationToken,
        expires: new Date(),
      });

      const verifyRequest = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token: verificationToken, email: testEmail }),
        headers: { 'Content-Type': 'application/json' },
      });

      const verifyResponse = await verifyPOST(verifyRequest);
      const verifyData = await verifyResponse.json();

      expect(verifyResponse.status).toBe(200);
      expect(verifyData.message).toBe('Email is already verified.');
      expect(mockPrisma.verificationToken.delete).toHaveBeenCalled();

      // Resend verification should also handle verified user
      mockPrisma.user.findUnique.mockResolvedValue(verifiedUser);

      const resendRequest = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
        method: 'POST',
        body: JSON.stringify({ email: testEmail }),
        headers: { 'Content-Type': 'application/json' },
      });

      const resendResponse = await resendPOST(resendRequest);
      const resendData = await resendResponse.json();

      expect(resendResponse.status).toBe(200);
      expect(resendData.message).toBe('Email is already verified.');
    });

    it('should enforce rate limiting on resend requests', async () => {
      const unverifiedUser = {
        id: userId,
        email: testEmail,
        password: hashedPassword,
        emailVerified: null,
        name: 'Test User',
        passwordResetToken: null,
        passwordResetExpires: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
      };

      mockPrisma.user.findUnique.mockResolvedValue(unverifiedUser);

      // Mock recent token (within 5 minutes)
      mockPrisma.verificationToken.findFirst.mockResolvedValue({
        identifier: testEmail,
        token: 'recent-token',
        expires: new Date(Date.now() + 23 * 60 * 60 * 1000), // 23 hours from now
      });

      const resendRequest = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
        method: 'POST',
        body: JSON.stringify({ email: testEmail }),
        headers: { 'Content-Type': 'application/json' },
      });

      const resendResponse = await resendPOST(resendRequest);
      const resendData = await resendResponse.json();

      expect(resendResponse.status).toBe(429);
      expect(resendData.error).toBe('A verification email was recently sent. Please wait 5 minutes before requesting another.');
    });
  });

  describe('Error Scenarios', () => {
    it('should handle invalid token gracefully', async () => {
      mockPrisma.verificationToken.findUnique.mockResolvedValue(null);

      const verifyRequest = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token: 'invalid-token', email: testEmail }),
        headers: { 'Content-Type': 'application/json' },
      });

      const verifyResponse = await verifyPOST(verifyRequest);
      const verifyData = await verifyResponse.json();

      expect(verifyResponse.status).toBe(400);
      expect(verifyData.error).toBe('Invalid verification token.');
    });

    it('should handle email mismatch', async () => {
      mockPrisma.verificationToken.findUnique.mockResolvedValue({
        identifier: '<EMAIL>',
        token: verificationToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      const verifyRequest = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token: verificationToken, email: testEmail }),
        headers: { 'Content-Type': 'application/json' },
      });

      const verifyResponse = await verifyPOST(verifyRequest);
      const verifyData = await verifyResponse.json();

      expect(verifyResponse.status).toBe(400);
      expect(verifyData.error).toBe('Invalid verification token.');
    });

    it('should handle non-existent user during verification', async () => {
      mockPrisma.verificationToken.findUnique.mockResolvedValue({
        identifier: testEmail,
        token: verificationToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      mockPrisma.user.findUnique.mockResolvedValue(null);

      const verifyRequest = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token: verificationToken, email: testEmail }),
        headers: { 'Content-Type': 'application/json' },
      });

      const verifyResponse = await verifyPOST(verifyRequest);
      const verifyData = await verifyResponse.json();

      expect(verifyResponse.status).toBe(404);
      expect(verifyData.error).toBe('User not found.');
    });
  });
});
