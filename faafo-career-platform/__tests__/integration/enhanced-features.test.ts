/**
 * Enhanced Features Integration Tests
 * Tests for new Community Forum and Progress Tracking enhancements
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock Next.js modules
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}));

jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
      },
    },
    status: 'authenticated',
  }),
}));

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  default: {
    user: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    forumPost: {
      findMany: jest.fn(),
      count: jest.fn(),
    },
    userGoal: {
      findMany: jest.fn(),
      create: jest.fn(),
    },
    userAchievement: {
      findMany: jest.fn(),
    },
    userLearningProgress: {
      findMany: jest.fn(),
    },
    achievement: {
      findMany: jest.fn(),
    },
  },
}));

describe('Enhanced Community Forum Features', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('User Mention System', () => {
    it('should search for users when typing @', async () => {
      const mockUsers = [
        {
          id: 'user-1',
          name: 'John Doe',
          email: '<EMAIL>',
          profile: {
            firstName: 'John',
            lastName: 'Doe',
            profilePictureUrl: null,
          },
        },
        {
          id: 'user-2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          profile: {
            firstName: 'Jane',
            lastName: 'Smith',
            profilePictureUrl: 'https://example.com/jane.jpg',
          },
        },
      ];

      (global.fetch as jest.MockedFunction<typeof fetch>) = jest.fn().mockImplementation(() =>
        Promise.resolve({
          ok: true,
          json: async () => ({ users: mockUsers }),
        } as any)
      );

      const response = await fetch('/api/users/search?q=john&limit=5');
      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(data.users).toHaveLength(2);
      expect(data.users[0].name).toBe('John Doe');
    });

    it('should handle user mention insertion', () => {
      const content = 'Hello @john, how are you?';
      const mentionRegex = /@(\w+)/g;
      const mentions = content.match(mentionRegex);

      expect(mentions).toEqual(['@john']);
    });
  });

  describe('Forum Search Functionality', () => {
    it('should search posts with various filters', async () => {
      const mockSearchResults = {
        posts: [
          {
            id: 'post-1',
            title: 'React Development Tips',
            content: 'Here are some React tips...',
            author: {
              id: 'user-1',
              name: 'John Doe',
              email: '<EMAIL>',
            },
            category: {
              id: 'cat-1',
              name: 'Development',
              slug: 'development',
            },
            _count: {
              replies: 5,
              reactions: 10,
              bookmarks: 3,
            },
          },
        ],
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          pages: 1,
        },
      };

      (global.fetch as jest.MockedFunction<typeof fetch>) = jest.fn().mockImplementation(() =>
        Promise.resolve({
          ok: true,
          json: async () => mockSearchResults,
        } as any)
      );

      const searchParams = new URLSearchParams({
        q: 'React',
        category: 'development',
        sortBy: 'newest',
      });

      const response = await fetch(`/api/forum/search?${searchParams.toString()}`);
      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(data.posts).toHaveLength(1);
      expect(data.posts[0].title).toBe('React Development Tips');
    });

    it('should handle advanced search filters', () => {
      const filters = {
        query: 'React development',
        category: 'SKILLS',
        author: 'john',
        dateRange: 'week',
        sortBy: 'most-replies',
        tags: ['react', 'frontend'],
      };

      const params = new URLSearchParams();
      if (filters.query) params.append('q', filters.query);
      if (filters.category) params.append('category', filters.category);
      if (filters.author) params.append('author', filters.author);
      if (filters.dateRange) params.append('dateRange', filters.dateRange);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.tags.length > 0) params.append('tags', filters.tags.join(','));

      const queryString = params.toString();
      expect(queryString).toContain('q=React+development');
      expect(queryString).toContain('category=SKILLS');
      expect(queryString).toContain('tags=react%2Cfrontend');
    });
  });

  describe('Goal Templates System', () => {
    it('should provide predefined goal templates', () => {
      const templates = [
        {
          id: 'learn-react',
          title: 'Master React Development',
          category: 'SKILLS',
          type: 'MONTHLY',
          targetValue: 5,
          difficulty: 'INTERMEDIATE',
        },
        {
          id: 'daily-coding',
          title: 'Daily Coding Practice',
          category: 'SKILLS',
          type: 'DAILY',
          targetValue: 1,
          difficulty: 'BEGINNER',
        },
      ];

      const filteredTemplates = templates.filter(
        template => template.difficulty === 'INTERMEDIATE'
      );

      expect(filteredTemplates).toHaveLength(1);
      expect(filteredTemplates[0].title).toBe('Master React Development');
    });

    it('should create goal from template', async () => {
      const template = {
        title: 'Master React Development',
        description: 'Complete a comprehensive React learning path',
        category: 'SKILLS',
        type: 'MONTHLY',
        targetValue: 5,
      };

      const mockGoal = {
        id: 'goal-1',
        userId: 'test-user-id',
        ...template,
        status: 'ACTIVE',
        currentValue: 0,
        createdAt: new Date(),
      };

      (global.fetch as jest.MockedFunction<typeof fetch>) = jest.fn().mockImplementation(() =>
        Promise.resolve({
          ok: true,
          json: async () => mockGoal,
        } as any)
      );

      const response = await fetch('/api/goals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(template),
      });

      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(data.title).toBe('Master React Development');
      expect(data.category).toBe('SKILLS');
    });
  });

  describe('Progress Analytics', () => {
    it('should calculate goal statistics', async () => {
      const mockAnalytics = {
        goalStats: {
          total: 10,
          completed: 7,
          active: 3,
          completionRate: 70,
          averageTimeToComplete: 15,
        },
        categoryBreakdown: [
          {
            category: 'SKILLS',
            count: 5,
            completed: 4,
            percentage: 80,
          },
          {
            category: 'CERTIFICATIONS',
            count: 3,
            completed: 2,
            percentage: 67,
          },
        ],
        streakData: {
          currentStreak: 7,
          longestStreak: 14,
          totalActiveDays: 45,
        },
        insights: [
          {
            type: 'success',
            title: 'Excellent Goal Achievement!',
            description: 'You have a 70% goal completion rate.',
          },
        ],
      };

      (global.fetch as jest.MockedFunction<typeof fetch>) = jest.fn().mockImplementation(() =>
        Promise.resolve({
          ok: true,
          json: async () => mockAnalytics,
        } as any)
      );

      const response = await fetch('/api/progress/analytics?range=6months');
      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(data.goalStats.completionRate).toBe(70);
      expect(data.streakData.currentStreak).toBe(7);
      expect(data.insights).toHaveLength(1);
    });

    it('should generate meaningful insights', () => {
      const completionRate = 85;
      const currentStreak = 10;
      const activeGoals = 2;

      const insights = [];

      if (completionRate >= 80) {
        insights.push({
          type: 'success',
          title: 'Excellent Goal Achievement!',
          description: `You have a ${completionRate}% goal completion rate.`,
        });
      }

      if (currentStreak >= 7) {
        insights.push({
          type: 'success',
          title: 'Amazing Learning Streak!',
          description: `You've been consistently learning for ${currentStreak} days.`,
        });
      }

      if (activeGoals <= 3) {
        insights.push({
          type: 'info',
          title: 'Good Goal Focus',
          description: `You have ${activeGoals} active goals, which is manageable.`,
        });
      }

      expect(insights).toHaveLength(3);
      expect(insights[0].type).toBe('success');
      expect(insights[1].title).toBe('Amazing Learning Streak!');
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete user workflow', async () => {
      // 1. User searches for posts
      let callCount = 0;
      (global.fetch as jest.MockedFunction<typeof fetch>) = jest.fn().mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          return Promise.resolve({
            ok: true,
            json: async () => ({ posts: [] }),
          } as any);
        } else if (callCount === 2) {
          return Promise.resolve({
            ok: true,
            json: async () => ({ id: 'goal-1', title: 'Learn React' }),
          } as any);
        } else {
          return Promise.resolve({
            ok: true,
            json: async () => ({ goalStats: { total: 1, active: 1 } }),
          } as any);
        }
      });

      // Search posts
      const searchResponse = await fetch('/api/forum/search?q=react');
      expect(searchResponse.ok).toBe(true);

      // Create goal
      const goalResponse = await fetch('/api/goals', {
        method: 'POST',
        body: JSON.stringify({ title: 'Learn React' }),
      });
      expect(goalResponse.ok).toBe(true);

      // View analytics
      const analyticsResponse = await fetch('/api/progress/analytics');
      expect(analyticsResponse.ok).toBe(true);
    });
  });
});

describe('Error Handling', () => {
  it('should handle API errors gracefully', async () => {
    (global.fetch as jest.MockedFunction<typeof fetch>) = jest.fn().mockImplementation(() =>
      Promise.resolve({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Internal server error' }),
      } as any)
    );

    const response = await fetch('/api/forum/search?q=test');
    expect(response.ok).toBe(false);
    expect(response.status).toBe(500);
  });

  it('should validate user input', () => {
    const validateGoalData = (data: any) => {
      const errors = [];
      
      if (!data.title || data.title.trim().length === 0) {
        errors.push('Title is required');
      }
      
      if (data.targetValue <= 0) {
        errors.push('Target value must be greater than 0');
      }
      
      return errors;
    };

    const invalidData = { title: '', targetValue: -1 };
    const errors = validateGoalData(invalidData);
    
    expect(errors).toHaveLength(2);
    expect(errors).toContain('Title is required');
    expect(errors).toContain('Target value must be greater than 0');
  });
});
