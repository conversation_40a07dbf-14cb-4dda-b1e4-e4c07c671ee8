/**
 * Edge Case Handler Performance Tests
 * 
 * Performance tests for Edge Case Handler Performance measuring response times, throughput, and resource usage.
 * 
 * @category performance
 * @requires Performance monitoring, load testing
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { performance } from 'perf_hooks';

// Mock environment variables for testing
process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key-for-performance-testing';
(process.env as any).NODE_ENV = 'test';

// Mock Prisma with performance-optimized implementations
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn().mockImplementation((params) => {
        // Simulate database latency
        return new Promise(resolve => {
          setTimeout(() => {
            if (params.where.id.includes('test') || params.where.id.includes('perf')) {
              resolve({ 
                id: params.where.id, 
                email: `${params.where.id}@example.com`,
                name: `Test User ${params.where.id}`,
                createdAt: new Date()
              });
            } else {
              resolve(null);
            }
          }, 10); // 10ms simulated DB latency
        });
      }),
    },
    skill: {
      findMany: jest.fn().mockImplementation((params) => {
        return new Promise(resolve => {
          setTimeout(() => {
            const skillMap = {
              'javascript': { id: 'javascript', name: 'JavaScript', category: 'Programming' },
              'react': { id: 'react', name: 'React', category: 'Frontend' },
              'nodejs': { id: 'nodejs', name: 'Node.js', category: 'Backend' },
              'python': { id: 'python', name: 'Python', category: 'Programming' },
              'typescript': { id: 'typescript', name: 'TypeScript', category: 'Programming' }
            };
            
            if (params.where?.id?.in) {
              resolve(
                params.where.id.in.map((id: string) => skillMap[id as keyof typeof skillMap]).filter(Boolean)
              );
            } else {
              resolve(Object.values(skillMap));
            }
          }, 5); // 5ms simulated DB latency
        });
      }),
      findFirst: jest.fn().mockImplementation((params) => {
        return new Promise(resolve => {
          setTimeout(() => {
            const skillName = params.where?.name?.equals?.toLowerCase() || params.where?.name?.equals;
            const skillMap = {
              'javascript': { id: 'javascript', name: 'JavaScript', category: 'Programming' },
              'react': { id: 'react', name: 'React', category: 'Frontend' },
              'nodejs': { id: 'nodejs', name: 'Node.js', category: 'Backend' },
              'python': { id: 'python', name: 'Python', category: 'Programming' },
              'typescript': { id: 'typescript', name: 'TypeScript', category: 'Programming' }
            };
            resolve(skillMap[skillName as keyof typeof skillMap] || null);
          }, 5);
        });
      }),
    },
    skillAssessment: {
      create: jest.fn().mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({ id: 'test-assessment', userId: 'test-user' });
          }, 15); // 15ms simulated DB write latency
        });
      }),
    },
    learningPath: {
      create: jest.fn().mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({ id: 'test-path', userId: 'test-user' });
          }, 15);
        });
      }),
    },
    skillMarketData: {
      create: jest.fn().mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({ id: 'test-market-data', skillId: 'javascript' });
          }, 15);
        });
      }),
      findFirst: jest.fn().mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              demand: 80,
              averageSalary: 95000,
              growth: 15,
              dataDate: new Date(),
              metadata: {}
            });
          }, 10);
        });
      }),
    },
    careerPath: {
      findFirst: jest.fn().mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({ 
              id: 'test-career-path', 
              title: 'Full Stack Developer',
              skills: []
            });
          }, 10);
        });
      }),
    },
  },
}));

import { edgeCaseHandlerService } from '@/lib/skills/EdgeCaseHandlerService';

describe('EdgeCaseHandler Performance Tests', () => {
  beforeEach(() => {
    // Clear performance caches before each test
    edgeCaseHandlerService.clearPerformanceCaches();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Skill Assessment Performance with EdgeCaseHandler', () => {
    it('should complete skill assessment within 2 seconds', async () => {
      const startTime = performance.now();

      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'perf-test-user-001',
        skillIds: ['javascript', 'react'],
        assessmentType: 'comprehensive'
      });

      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(2000); // 2 seconds
      expect(result.success).toBe(true);
    });

    it('should handle concurrent assessments efficiently', async () => {
      const startTime = performance.now();
      const concurrentRequests = 5;
      
      const promises = Array.from({ length: concurrentRequests }, (_, i) => 
        edgeCaseHandlerService.createSkillAssessmentWithDatabase({
          userId: `perf-test-user-${i}`,
          skillIds: ['javascript', 'react'],
          assessmentType: 'quick'
        })
      );

      const results = await Promise.all(promises);
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete 5 concurrent requests in under 3 seconds
      expect(duration).toBeLessThan(3000);
      
      // All requests should succeed
      results.forEach(result => {
        expect(result.success).toBe(true);
      });

      // Check performance metrics
      const metrics = edgeCaseHandlerService.getPerformanceMetrics();
      expect(metrics.cacheStats.user.size).toBeGreaterThan(0);
      expect(metrics.cacheStats.skill.size).toBeGreaterThan(0);
    });

    it('should benefit from caching on repeated requests', async () => {
      const userId = 'cache-test-user';
      const skillIds = ['javascript', 'react'];

      // First request (cache miss)
      const start1 = performance.now();
      const result1 = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId,
        skillIds,
        assessmentType: 'comprehensive'
      });
      const duration1 = performance.now() - start1;

      // Second request (cache hit)
      const start2 = performance.now();
      const result2 = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId,
        skillIds,
        assessmentType: 'comprehensive'
      });
      const duration2 = performance.now() - start2;

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      
      // Second request should be faster due to caching
      expect(duration2).toBeLessThan(duration1);

      // Check cache statistics
      const metrics = edgeCaseHandlerService.getPerformanceMetrics();
      expect(metrics.cacheStats.user.hits).toBeGreaterThan(0);
      expect(metrics.cacheStats.skill.hits).toBeGreaterThan(0);
    });
  });

  describe('Learning Path Performance with EdgeCaseHandler', () => {
    it('should generate learning paths within 3 seconds', async () => {
      const startTime = performance.now();

      const result = await edgeCaseHandlerService.generateLearningPathWithDatabase({
        userId: 'perf-test-user-002',
        targetRole: 'Full Stack Developer',
        currentSkills: [
          { skill: 'JavaScript', level: 6 },
          { skill: 'React', level: 5 }
        ],
        timeframe: 6,
        budget: 1000,
        availability: 10
      });

      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(3000); // 3 seconds
      expect(result.success).toBe(true);
    });

    it('should handle batch learning path generation efficiently', async () => {
      const startTime = performance.now();
      const batchSize = 3;
      
      const promises = Array.from({ length: batchSize }, (_, i) => 
        edgeCaseHandlerService.generateLearningPathWithDatabase({
          userId: `batch-user-${i}`,
          targetRole: 'Full Stack Developer',
          currentSkills: [
            { skill: 'JavaScript', level: 5 + i },
            { skill: 'React', level: 4 + i }
          ],
          timeframe: 6,
          budget: 1000,
          availability: 10
        })
      );

      const results = await Promise.all(promises);
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete 3 learning path generations in under 5 seconds
      expect(duration).toBeLessThan(5000);
      
      // All requests should succeed
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Market Data Performance with EdgeCaseHandler', () => {
    it('should retrieve market data within 1 second', async () => {
      const startTime = performance.now();

      const result = await edgeCaseHandlerService.getMarketDataWithDatabase({
        skill: 'JavaScript',
        location: 'San Francisco'
      });

      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(1000); // 1 second
      expect(result.success).toBe(true);
    });

    it('should handle multiple market data requests efficiently', async () => {
      const skills = ['JavaScript', 'Python', 'React', 'Node.js', 'TypeScript'];
      const startTime = performance.now();
      
      const promises = skills.map(skill => 
        edgeCaseHandlerService.getMarketDataWithDatabase({ skill })
      );

      const results = await Promise.all(promises);
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete 5 market data requests in under 2 seconds
      expect(duration).toBeLessThan(2000);
      
      // All requests should succeed
      results.forEach(result => {
        expect(result.success).toBe(true);
      });

      // Check that caching is working
      const metrics = edgeCaseHandlerService.getPerformanceMetrics();
      expect(metrics.cacheStats.marketData.size).toBeGreaterThan(0);
    });
  });

  describe('Memory Usage and Resource Management', () => {
    it('should maintain reasonable memory usage during extended operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple operations
      for (let i = 0; i < 10; i++) {
        await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
          userId: `memory-test-user-${i}`,
          skillIds: ['javascript'],
          assessmentType: 'quick'
        });
        
        await edgeCaseHandlerService.getMarketDataWithDatabase({
          skill: `test-skill-${i}`
        });
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 10MB for 20 operations)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    it('should provide comprehensive performance metrics', async () => {
      // Perform various operations
      await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'metrics-test-user',
        skillIds: ['javascript', 'react'],
        assessmentType: 'comprehensive'
      });

      await edgeCaseHandlerService.generateLearningPathWithDatabase({
        userId: 'metrics-test-user',
        targetRole: 'Frontend Developer',
        currentSkills: [{ skill: 'JavaScript', level: 5 }],
        timeframe: 3
      });

      await edgeCaseHandlerService.getMarketDataWithDatabase({
        skill: 'React'
      });

      const metrics = edgeCaseHandlerService.getPerformanceMetrics();
      
      expect(metrics).toHaveProperty('metrics');
      expect(metrics).toHaveProperty('cacheStats');
      
      expect(metrics.cacheStats).toHaveProperty('user');
      expect(metrics.cacheStats).toHaveProperty('skill');
      expect(metrics.cacheStats).toHaveProperty('marketData');
      
      // Should have cache hits from operations
      expect(metrics.cacheStats.user.size).toBeGreaterThan(0);
      expect(metrics.cacheStats.skill.size).toBeGreaterThan(0);
      expect(metrics.cacheStats.marketData.size).toBeGreaterThan(0);
    });
  });

  describe('Error Handling Performance', () => {
    it('should handle errors efficiently without performance degradation', async () => {
      const startTime = performance.now();

      // Test with non-existent user (should fail gracefully)
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'non-existent-user',
        skillIds: ['javascript'],
        assessmentType: 'quick'
      });

      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(500); // Should fail fast
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('VALIDATION_ERROR');
    });

    it('should recover quickly from temporary failures', async () => {
      // Mock a temporary database failure
      const mockPrisma = require('@/lib/prisma').prisma;
      const originalFindUnique = mockPrisma.user.findUnique;
      
      // First call fails
      mockPrisma.user.findUnique.mockRejectedValueOnce(new Error('Temporary DB error'));

      const start1 = performance.now();
      const result1 = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'recovery-test-user',
        skillIds: ['javascript'],
        assessmentType: 'quick'
      });
      const duration1 = performance.now() - start1;

      // Restore normal behavior
      mockPrisma.user.findUnique.mockImplementation(originalFindUnique);

      // Second call should succeed
      const start2 = performance.now();
      const result2 = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'recovery-test-user',
        skillIds: ['javascript'],
        assessmentType: 'quick'
      });
      const duration2 = performance.now() - start2;

      expect(result1.success).toBe(false);
      expect(result2.success).toBe(true);
      
      // Both operations should complete quickly
      expect(duration1).toBeLessThan(1000);
      expect(duration2).toBeLessThan(1000);
    });
  });
});
