/**
 * Skill Gap Security Tests
 * 
 * Security tests for Skill Gap Security validating authentication, authorization, and vulnerability protection.
 * 
 * @category security
 * @requires Security testing utilities, mock authentication
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock environment variables for testing
process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key-for-security-testing';
(process.env as any).NODE_ENV = 'test';

// Mock Prisma for security testing
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn().mockImplementation((params) => {
        if (params.where.id === 'valid-user-123') {
          return Promise.resolve({ 
            id: 'valid-user-123', 
            email: '<EMAIL>',
            name: 'Test User',
            role: 'USER'
          });
        }
        return Promise.resolve(null);
      }),
    },
    skill: {
      findMany: jest.fn().mockResolvedValue([
        { id: 'javascript', name: 'JavaScript', category: 'Programming' },
        { id: 'react', name: 'React', category: 'Frontend' }
      ]),
    },
    skillAssessment: {
      create: jest.fn().mockResolvedValue({ id: 'test-assessment', userId: 'valid-user-123' }),
      findMany: jest.fn().mockResolvedValue([]),
    },
  },
}));

import { edgeCaseHandlerService } from '@/lib/skills/EdgeCaseHandlerService';

describe('Skill Gap Analyzer Security Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Input Validation Security', () => {
    it('should reject SQL injection attempts in user ID', async () => {
      const maliciousUserId = "'; DROP TABLE users; --";
      
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: maliciousUserId,
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('SECURITY_ERROR');
      expect(result.securityAlert).toBe(true);
      expect(result.error).toContain('Malicious input detected');
    });

    it('should reject XSS attempts in skill names', async () => {
      const xssSkillName = '<script>alert("xss")</script>';
      
      const result = await edgeCaseHandlerService.getMarketDataWithDatabase({
        skill: xssSkillName
      });

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('SECURITY_ERROR');
      expect(result.securityAlert).toBe(true);
      expect(result.error).toContain('Malicious input detected');
    });

    it('should reject NoSQL injection attempts', async () => {
      const nosqlInjection = { $ne: null };
      
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: nosqlInjection as any,
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('VALIDATION_ERROR');
    });

    it('should sanitize and validate all string inputs', async () => {
      const dirtyInput = '  <script>alert("test")</script>  ';
      
      const result = await edgeCaseHandlerService.getMarketDataWithDatabase({
        skill: dirtyInput,
        location: dirtyInput
      });

      // Should either reject or sanitize the input
      if (result.success) {
        expect(result.sanitizedInput?.skill).not.toContain('<script>');
        expect(result.sanitizedInput?.location).not.toContain('<script>');
      } else {
        expect(result.errorType).toBe('SECURITY_ERROR');
      }
    });

    it('should reject excessively long inputs (DoS protection)', async () => {
      const longString = 'a'.repeat(10000); // 10KB string
      
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: longString,
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('VALIDATION_ERROR');
      expect(result.error).toContain('Input too long');
    });

    it('should validate array inputs for injection attempts', async () => {
      const maliciousArray = ['javascript', "'; DROP TABLE skills; --", 'react'];
      
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'valid-user-123',
        skillIds: maliciousArray,
        assessmentType: 'comprehensive'
      });

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('SECURITY_ERROR');
      expect(result.securityAlert).toBe(true);
    });
  });

  describe('Authentication & Authorization Security', () => {
    it('should reject requests without valid user authentication', async () => {
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'non-existent-user',
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('VALIDATION_ERROR');
      expect(result.error).toBe('User not found');
    });

    it('should validate user permissions for sensitive operations', async () => {
      // Test with a user who shouldn't have access to admin features
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'valid-user-123',
        skillIds: ['javascript'],
        assessmentType: 'admin-only-assessment'
      });

      // Should either succeed with limited access or fail with permission error
      if (!result.success) {
        expect(result.errorType).toBe('VALIDATION_ERROR');
      }
    });

    it('should prevent unauthorized access to other users data', async () => {
      // Attempt to access another user's data
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'other-user-456',
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('VALIDATION_ERROR');
    });
  });

  describe('Rate Limiting & DoS Protection', () => {
    it('should handle rapid successive requests gracefully', async () => {
      const promises = Array.from({ length: 20 }, () =>
        edgeCaseHandlerService.createSkillAssessmentWithDatabase({
          userId: 'valid-user-123',
          skillIds: ['javascript'],
          assessmentType: 'quick'
        })
      );

      const results = await Promise.all(promises);
      
      // Should either succeed with rate limiting or fail gracefully
      const successCount = results.filter(r => r.success).length;
      const rateLimitedCount = results.filter(r => 
        !r.success && r.errorType === 'RESOURCE_ERROR'
      ).length;

      expect(successCount + rateLimitedCount).toBe(20);
    });

    it('should protect against memory exhaustion attacks', async () => {
      const largeSkillArray = Array.from({ length: 1000 }, (_, i) => `skill-${i}`);
      
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'valid-user-123',
        skillIds: largeSkillArray,
        assessmentType: 'comprehensive'
      });

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('VALIDATION_ERROR');
      expect(result.error).toContain('Too many skills');
    });
  });

  describe('Data Sanitization & Output Security', () => {
    it('should sanitize output data to prevent XSS', async () => {
      // Mock a skill with potentially dangerous content
      const mockPrisma = require('@/lib/prisma').prisma;
      mockPrisma.skill.findMany.mockResolvedValueOnce([
        { 
          id: 'javascript', 
          name: '<script>alert("xss")</script>JavaScript', 
          category: 'Programming' 
        }
      ]);

      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'valid-user-123',
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      if (result.success && result.data) {
        // Output should be sanitized
        const outputString = JSON.stringify(result.data);
        expect(outputString).not.toContain('<script>');
        expect(outputString).not.toContain('alert(');
      }
    });

    it('should prevent information disclosure in error messages', async () => {
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'non-existent-user',
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      expect(result.success).toBe(false);
      // Error message should not reveal internal system details
      expect(result.error).not.toContain('database');
      expect(result.error).not.toContain('prisma');
      expect(result.error).not.toContain('internal');
      expect(result.error).not.toContain('stack');
    });

    it('should mask sensitive data in logs and responses', async () => {
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'valid-user-123',
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      // Check that sensitive data is not exposed
      const responseString = JSON.stringify(result);
      expect(responseString).not.toContain('password');
      expect(responseString).not.toContain('secret');
      expect(responseString).not.toContain('token');
      expect(responseString).not.toContain('api_key');
    });
  });

  describe('Encryption & Data Protection', () => {
    it('should handle encrypted data properly', async () => {
      // Test that the system can handle encrypted user IDs
      const encryptedUserId = 'enc_valid-user-123';
      
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: encryptedUserId,
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      // Should either decrypt and process or fail gracefully
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('errorType');
    });

    it('should validate data integrity', async () => {
      // Test with tampered data
      const tamperedData = {
        userId: 'valid-user-123',
        skillIds: ['javascript'],
        assessmentType: 'comprehensive',
        __proto__: { malicious: true }
      };

      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase(tamperedData);

      // Should detect and reject tampered data
      if (!result.success) {
        expect(result.errorType).toBe('SECURITY_ERROR');
      }
    });
  });

  describe('Security Headers & CSRF Protection', () => {
    it('should validate CSRF tokens when required', async () => {
      // This would typically be tested at the API route level
      // For now, we test that the service can handle CSRF validation
      const resultWithoutCSRF = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'valid-user-123',
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      // Should succeed in test environment, but in production would require CSRF token
      expect(resultWithoutCSRF).toHaveProperty('success');
    });

    it('should handle secure session management', async () => {
      // Test session-based security
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'valid-user-123',
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      // Should not expose session information
      const responseString = JSON.stringify(result);
      expect(responseString).not.toContain('session');
      expect(responseString).not.toContain('cookie');
    });
  });

  describe('Audit Logging & Security Monitoring', () => {
    it('should log security events for monitoring', async () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      // Trigger a security event
      await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: "'; DROP TABLE users; --",
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      // Should log security warning
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Security alert')
      );

      consoleSpy.mockRestore();
    });

    it('should track failed authentication attempts', async () => {
      const attempts = [];
      
      for (let i = 0; i < 5; i++) {
        const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
          userId: 'non-existent-user',
          skillIds: ['javascript'],
          assessmentType: 'comprehensive'
        });
        attempts.push(result);
      }

      // Should track multiple failed attempts
      attempts.forEach(attempt => {
        expect(attempt.success).toBe(false);
        expect(attempt.errorType).toBe('VALIDATION_ERROR');
      });
    });
  });

  describe('Third-Party Integration Security', () => {
    it('should validate external API responses', async () => {
      // Test market data from external sources
      const result = await edgeCaseHandlerService.getMarketDataWithDatabase({
        skill: 'JavaScript'
      });

      if (result.success && result.data) {
        // Should validate that external data is safe
        expect(result.data).not.toHaveProperty('__proto__');
        expect(result.data).not.toHaveProperty('constructor');
      }
    });

    it('should handle API key security properly', async () => {
      // Test that API keys are not exposed
      const result = await edgeCaseHandlerService.getMarketDataWithDatabase({
        skill: 'JavaScript'
      });

      const responseString = JSON.stringify(result);
      expect(responseString).not.toContain('api_key');
      expect(responseString).not.toContain('GOOGLE_GEMINI_API_KEY');
      expect(responseString).not.toContain('secret');
    });
  });

  describe('Content Security Policy (CSP) Compliance', () => {
    it('should generate CSP-compliant content', async () => {
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
        userId: 'valid-user-123',
        skillIds: ['javascript'],
        assessmentType: 'comprehensive'
      });

      if (result.success && result.data) {
        const content = JSON.stringify(result.data);
        // Should not contain inline scripts or unsafe content
        expect(content).not.toContain('javascript:');
        expect(content).not.toContain('data:text/html');
        expect(content).not.toContain('vbscript:');
      }
    });
  });
});
