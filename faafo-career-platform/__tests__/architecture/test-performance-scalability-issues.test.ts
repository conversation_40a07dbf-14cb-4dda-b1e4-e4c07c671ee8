/**
 * Test Performance and Scalability Issues Tests
 * 
 * These tests prove test performance problems including slow execution times,
 * memory leaks, and unbounded resource usage during test runs.
 * 
 * EXPECTED TO FAIL - These tests demonstrate performance issues that need fixing.
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import fs from 'fs';
import path from 'path';

describe('Test Performance and Scalability Issues', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('CRITICAL ISSUE 1: Slow Test Execution Times', () => {
    it('should fail - test suite execution time exceeds acceptable limits', async () => {
      // Measure actual test execution time
      const startTime = Date.now();
      
      // Run a subset of tests to measure performance
      const testFiles: string[] = [];
      const testDirectory = path.join(process.cwd(), '__tests__');
      
      function findTestFiles(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            findTestFiles(filePath);
          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
            testFiles.push(filePath);
          }
        });
      }
      
      findTestFiles(testDirectory);
      
      // Analyze test files for performance issues
      const slowTestFiles = [];
      
      testFiles.forEach(testFile => {
        try {
          const content = fs.readFileSync(testFile, 'utf8');
          
          // Count potentially slow operations
          const slowOperations = [
            /setTimeout|setInterval/gi,
            /fetch\(/gi,
            /prisma\./gi,
            /database/gi,
            /ai-service|gemini/gi
          ];
          
          let slowOpCount = 0;
          slowOperations.forEach(pattern => {
            slowOpCount += (content.match(pattern) || []).length;
          });
          
          const testCount = (content.match(/it\(/g) || []).length;
          const slowOpsPerTest = testCount > 0 ? slowOpCount / testCount : 0;
          
          // If more than 3 slow operations per test, it's likely slow
          if (slowOpsPerTest > 3) {
            slowTestFiles.push({ 
              file: testFile, 
              slowOpCount, 
              testCount, 
              slowOpsPerTest: Math.round(slowOpsPerTest * 100) / 100 
            });
          }
        } catch (error) {
          // Skip files that can't be read
        }
      });
      
      const executionTime = Date.now() - startTime;
      
      // EXPECTED TO FAIL: Test execution should be fast (under 100ms for analysis)
      expect(executionTime).toBeLessThan(100);
      
      // EXPECTED TO FAIL: Should not have many slow test files
      expect(slowTestFiles.length).toBeLessThan(5);
    });

    it('should fail - individual tests take too long to execute', () => {
      // Check for tests with excessive timeouts or slow operations
      const testDirectory = path.join(process.cwd(), '__tests__');
      const slowIndividualTests = [];
      
      function analyzeTestSpeed(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            analyzeTestSpeed(filePath);
          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
            try {
              const content = fs.readFileSync(filePath, 'utf8');
              
              // Look for tests with long timeouts
              const timeoutMatches = content.match(/\.timeout\((\d+)\)/g) || [];
              timeoutMatches.forEach(timeoutMatch => {
                const timeoutNumMatch = timeoutMatch.match(/\d+/);
                if (!timeoutNumMatch) return;
                const timeout = parseInt(timeoutNumMatch[0]);
                if (timeout > 30000) { // More than 30 seconds
                  slowIndividualTests.push({ 
                    file: filePath, 
                    issue: `Excessive timeout: ${timeout}ms` 
                  });
                }
              });
              
              // Look for synchronous operations that should be async
              const syncOperations = [
                /fs\.readFileSync/gi,
                /fs\.writeFileSync/gi,
                /JSON\.parse.*fs\./gi
              ];
              
              let syncOpCount = 0;
              syncOperations.forEach(pattern => {
                syncOpCount += (content.match(pattern) || []).length;
              });
              
              if (syncOpCount > 5) {
                slowIndividualTests.push({ 
                  file: filePath, 
                  issue: `Too many synchronous operations: ${syncOpCount}` 
                });
              }
            } catch (error) {
              // Skip files that can't be read
            }
          }
        });
      }
      
      analyzeTestSpeed(testDirectory);
      
      // EXPECTED TO FAIL: Individual tests should not be slow
      expect(slowIndividualTests.length).toBe(0);
    });
  });

  describe('CRITICAL ISSUE 2: Memory Leaks in Tests', () => {
    it('should fail - tests create memory leaks through unclosed resources', () => {
      // Check for potential memory leak patterns
      const testDirectory = path.join(process.cwd(), '__tests__');
      const memoryLeakIssues = [];
      
      function analyzeMemoryLeaks(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            analyzeMemoryLeaks(filePath);
          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
            try {
              const content = fs.readFileSync(filePath, 'utf8');
              
              // Check for unclosed resources
              const resourceCreation = [
                /new PrismaClient/gi,
                /createConnection/gi,
                /setInterval/gi,
                /setTimeout.*\d{4,}/gi, // Long timeouts
                /addEventListener/gi
              ];
              
              const resourceCleanup = [
                /disconnect|close|clear|remove/gi,
                /afterEach|afterAll/gi
              ];
              
              let creationCount = 0;
              resourceCreation.forEach(pattern => {
                creationCount += (content.match(pattern) || []).length;
              });
              
              let cleanupCount = 0;
              resourceCleanup.forEach(pattern => {
                cleanupCount += (content.match(pattern) || []).length;
              });
              
              // If creating resources without cleanup
              if (creationCount > 2 && cleanupCount === 0) {
                memoryLeakIssues.push({ 
                  file: filePath, 
                  creationCount, 
                  cleanupCount,
                  issue: 'Resources created without cleanup' 
                });
              }
              
              // Check for global variable accumulation
              const globalVarAssignments = (content.match(/global\.\w+\s*=/g) || []).length;
              if (globalVarAssignments > 3) {
                memoryLeakIssues.push({ 
                  file: filePath, 
                  issue: `Too many global variable assignments: ${globalVarAssignments}` 
                });
              }
            } catch (error) {
              // Skip files that can't be read
            }
          }
        });
      }
      
      analyzeMemoryLeaks(testDirectory);
      
      // EXPECTED TO FAIL: Tests should not create memory leaks
      expect(memoryLeakIssues.length).toBe(0);
    });

    it('should fail - test data accumulates without proper cleanup', () => {
      // Check for test data accumulation issues
      const testDirectory = path.join(process.cwd(), '__tests__');
      const dataAccumulationIssues = [];
      
      function analyzeDataAccumulation(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            analyzeDataAccumulation(filePath);
          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
            try {
              const content = fs.readFileSync(filePath, 'utf8');
              
              // Check for data creation without cleanup
              const dataCreation = [
                /\.create\(/gi,
                /\.insert\(/gi,
                /\.save\(/gi,
                /push\(/gi
              ];
              
              const dataCleanup = [
                /\.delete\(/gi,
                /\.remove\(/gi,
                /\.clear\(/gi,
                /\.truncate\(/gi,
                /\.pop\(/gi,
                /\.splice\(/gi
              ];
              
              let creationOps = 0;
              dataCreation.forEach(pattern => {
                creationOps += (content.match(pattern) || []).length;
              });
              
              let cleanupOps = 0;
              dataCleanup.forEach(pattern => {
                cleanupOps += (content.match(pattern) || []).length;
              });
              
              // If creating much more data than cleaning up
              if (creationOps > 5 && cleanupOps < creationOps * 0.5) {
                dataAccumulationIssues.push({ 
                  file: filePath, 
                  creationOps, 
                  cleanupOps,
                  ratio: Math.round((cleanupOps / creationOps) * 100) / 100
                });
              }
            } catch (error) {
              // Skip files that can't be read
            }
          }
        });
      }
      
      analyzeDataAccumulation(testDirectory);
      
      // EXPECTED TO FAIL: Test data should be properly cleaned up
      expect(dataAccumulationIssues.length).toBe(0);
    });
  });

  describe('CRITICAL ISSUE 3: Unbounded Resource Usage', () => {
    it('should fail - tests use unbounded arrays or objects that grow indefinitely', () => {
      // Check for unbounded data structures
      const testDirectory = path.join(process.cwd(), '__tests__');
      const unboundedResourceIssues = [];
      
      function analyzeUnboundedResources(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            analyzeUnboundedResources(filePath);
          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
            try {
              const content = fs.readFileSync(filePath, 'utf8');
              
              // Look for potentially unbounded operations
              const unboundedPatterns = [
                /while\s*\(true\)/gi,
                /for\s*\(\s*;\s*;\s*\)/gi,
                /setInterval(?!.*clear)/gi,
                /\.push\(.*\).*for|while/gi,
                /Array\(\d{4,}\)/gi // Large array creation
              ];
              
              let unboundedCount = 0;
              unboundedPatterns.forEach(pattern => {
                unboundedCount += (content.match(pattern) || []).length;
              });
              
              // Check for large test data generation
              const largeDataMatches = content.match(/Array\.from.*\d{3,}|new Array\(\d{3,}\)/gi) || [];
              
              if (unboundedCount > 0 || largeDataMatches.length > 2) {
                unboundedResourceIssues.push({ 
                  file: filePath, 
                  unboundedCount, 
                  largeDataCount: largeDataMatches.length,
                  issues: largeDataMatches
                });
              }
            } catch (error) {
              // Skip files that can't be read
            }
          }
        });
      }
      
      analyzeUnboundedResources(testDirectory);
      
      // EXPECTED TO FAIL: Tests should not use unbounded resources
      expect(unboundedResourceIssues.length).toBe(0);
    });

    it('should fail - concurrent test execution causes resource contention', () => {
      // Check for resource contention issues
      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');
      const resourceContentionIssues = [];
      
      if (fs.existsSync(jestConfigPath)) {
        const configContent = fs.readFileSync(jestConfigPath, 'utf8');
        
        // Check maxWorkers configuration
        const maxWorkersMatch = configContent.match(/maxWorkers:\s*['"`]?([^'"`\s,}]+)['"`]?/);
        const maxWorkers = maxWorkersMatch ? maxWorkersMatch[1] : '50%';
        
        // Check if maxWorkers is too high for resource-intensive tests
        if (maxWorkers === '100%' || (typeof maxWorkers === 'string' && maxWorkers.includes('%') && parseInt(maxWorkers) > 75)) {
          resourceContentionIssues.push('maxWorkers too high for resource-intensive tests');
        }
        
        // Check for database connection pooling issues
        const testDirectory = path.join(process.cwd(), '__tests__');
        let dbTestCount = 0;
        
        function countDbTests(dir: string) {
          if (!fs.existsSync(dir)) return;
          
          const files = fs.readdirSync(dir);
          files.forEach(file => {
            const filePath = path.join(dir, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isDirectory()) {
              countDbTests(filePath);
            } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
              try {
                const content = fs.readFileSync(filePath, 'utf8');
                if (content.includes('prisma') || content.includes('database')) {
                  dbTestCount++;
                }
              } catch (error) {
                // Skip files that can't be read
              }
            }
          });
        }
        
        countDbTests(testDirectory);
        
        // If many database tests with high concurrency
        if (dbTestCount > 5 && (maxWorkers === '100%' || parseInt(maxWorkers) > 50)) {
          resourceContentionIssues.push(`${dbTestCount} database tests with high concurrency (${maxWorkers})`);
        }
      }
      
      // EXPECTED TO FAIL: Should not have resource contention issues
      expect(resourceContentionIssues.length).toBe(0);
    });
  });

  describe('CRITICAL ISSUE 4: Test Suite Scalability Problems', () => {
    it('should fail - test suite does not scale with codebase growth', () => {
      // Analyze test suite scalability
      const testDirectory = path.join(process.cwd(), '__tests__');
      const srcDirectory = path.join(process.cwd(), 'src');
      
      let testFileCount = 0;
      let srcFileCount = 0;
      
      function countFiles(dir: string, isTestDir: boolean) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            countFiles(filePath, isTestDir);
          } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
            if (isTestDir && (file.includes('.test.') || file.includes('.spec.'))) {
              testFileCount++;
            } else if (!isTestDir && !file.includes('.test.') && !file.includes('.spec.')) {
              srcFileCount++;
            }
          }
        });
      }
      
      countFiles(testDirectory, true);
      countFiles(srcDirectory, false);
      
      const testCoverageRatio = srcFileCount > 0 ? testFileCount / srcFileCount : 0;
      
      // EXPECTED TO FAIL: Should have reasonable test coverage ratio (at least 0.5)
      expect(testCoverageRatio).toBeGreaterThan(0.5);
      
      // Check for test organization scalability
      const scalabilityIssues = [];
      
      // Tests should be organized in directories
      if (testFileCount > 20) {
        const testSubdirs = fs.readdirSync(testDirectory).filter(item => {
          const itemPath = path.join(testDirectory, item);
          return fs.statSync(itemPath).isDirectory();
        });
        
        if (testSubdirs.length < 3) {
          scalabilityIssues.push('Large test suite not properly organized into subdirectories');
        }
      }
      
      // EXPECTED TO FAIL: Should not have scalability issues
      expect(scalabilityIssues.length).toBe(0);
    });

    it('should fail - test execution time grows non-linearly with test count', () => {
      // Analyze test execution time scaling
      const testDirectory = path.join(process.cwd(), '__tests__');
      const performanceIssues = [];
      
      function analyzeTestComplexity(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            analyzeTestComplexity(filePath);
          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
            try {
              const content = fs.readFileSync(filePath, 'utf8');
              
              const testCount = (content.match(/it\(/g) || []).length;
              const complexOperations = [
                /nested.*describe/gi,
                /for.*for/gi, // Nested loops
                /while.*while/gi,
                /\.map.*\.filter.*\.reduce/gi // Chained operations
              ];
              
              let complexityScore = 0;
              complexOperations.forEach(pattern => {
                complexityScore += (content.match(pattern) || []).length * 2;
              });
              
              // Add base complexity for each test
              complexityScore += testCount;
              
              const complexityPerTest = testCount > 0 ? complexityScore / testCount : 0;
              
              // If complexity per test is too high
              if (complexityPerTest > 5) {
                performanceIssues.push({ 
                  file: filePath, 
                  testCount, 
                  complexityScore, 
                  complexityPerTest: Math.round(complexityPerTest * 100) / 100 
                });
              }
            } catch (error) {
              // Skip files that can't be read
            }
          }
        });
      }
      
      analyzeTestComplexity(testDirectory);
      
      // EXPECTED TO FAIL: Test complexity should be manageable
      expect(performanceIssues.length).toBe(0);
    });
  });
});
