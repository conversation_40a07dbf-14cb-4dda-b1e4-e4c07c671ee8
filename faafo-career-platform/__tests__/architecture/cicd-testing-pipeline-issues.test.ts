/**
 * CI/CD Testing Pipeline Issues Tests
 * 
 * These tests prove CI/CD pipeline issues including incomplete test scripts,
 * inadequate coverage thresholds, and missing automation.
 * 
 * EXPECTED TO FAIL - These tests demonstrate CI/CD pipeline issues that need fixing.
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import fs from 'fs';
import path from 'path';

describe('CI/CD Testing Pipeline Issues', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('CRITICAL ISSUE 1: Incomplete Test Scripts', () => {
    it('should fail - package.json test scripts are incomplete for CI/CD', () => {
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        const scripts = packageJson.scripts || {};
        const scriptIssues = [];
        
        // Required CI/CD test scripts
        const requiredScripts = {
          'test': 'Basic test execution',
          'test:ci': 'CI-specific test execution with coverage',
          'test:coverage': 'Coverage reporting',
          'test:watch': 'Development watch mode',
          'test:unit': 'Unit tests only',
          'test:integration': 'Integration tests only',
          'test:e2e': 'End-to-end tests',
          'test:performance': 'Performance tests',
          'test:security': 'Security tests'
        };
        
        Object.entries(requiredScripts).forEach(([script, description]) => {
          if (!scripts[script]) {
            scriptIssues.push(`Missing ${script}: ${description}`);
          }
        });
        
        // Check CI script quality
        const testCiScript = scripts['test:ci'];
        if (testCiScript) {
          // CI script should include coverage
          if (!testCiScript.includes('--coverage')) {
            scriptIssues.push('test:ci script missing --coverage flag');
          }
          
          // CI script should disable watch mode
          if (testCiScript.includes('--watch') && !testCiScript.includes('--watchAll=false')) {
            scriptIssues.push('test:ci script should disable watch mode');
          }
          
          // CI script should have proper exit codes
          if (!testCiScript.includes('--passWithNoTests=false')) {
            scriptIssues.push('test:ci script should fail when no tests found');
          }
        }
        
        // Check for parallel execution configuration
        const testScript = scripts['test'];
        if (testScript && !testScript.includes('--maxWorkers')) {
          scriptIssues.push('test script missing --maxWorkers configuration for CI');
        }
        
        // EXPECTED TO FAIL: All required CI/CD scripts should be present and properly configured
        expect(scriptIssues.length).toBe(0);
      } else {
        // EXPECTED TO FAIL: package.json should exist
        expect(fs.existsSync(packageJsonPath)).toBe(true);
      }
    });

    it('should fail - test scripts lack proper error handling and reporting', () => {
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        const scripts = packageJson.scripts || {};
        const errorHandlingIssues = [];
        
        // Check test scripts for error handling
        const testScripts = Object.entries(scripts).filter(([key]) => key.startsWith('test'));
        
        testScripts.forEach(([scriptName, scriptCommand]) => {
          const cmd = scriptCommand as string;
          // Should have proper exit code handling
          if (!cmd.includes('--') && !cmd.includes('&&')) {
            errorHandlingIssues.push(`${scriptName}: No error handling or chaining`);
          }

          // Coverage scripts should have threshold enforcement
          if ((scriptName as string).includes('coverage') && !cmd.includes('--coverageThreshold')) {
            errorHandlingIssues.push(`${scriptName}: Missing coverage threshold enforcement`);
          }
          
          // CI scripts should have proper reporting
          if (scriptName.includes('ci') && !scriptCommand.includes('--reporters')) {
            errorHandlingIssues.push(`${scriptName}: Missing CI reporters configuration`);
          }
        });
        
        // EXPECTED TO FAIL: Test scripts should have proper error handling
        expect(errorHandlingIssues.length).toBe(0);
      }
    });
  });

  describe('CRITICAL ISSUE 2: Inadequate Coverage Thresholds', () => {
    it('should fail - coverage thresholds are too low for production code', () => {
      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');
      
      if (fs.existsSync(jestConfigPath)) {
        const configContent = fs.readFileSync(jestConfigPath, 'utf8');
        const coverageIssues = [];
        
        // Extract coverage thresholds
        const coverageMatch = configContent.match(/coverageThreshold:\s*{[^}]*global:\s*{([^}]+)}/s);
        
        if (coverageMatch) {
          const thresholds = coverageMatch[1];
          
          // Required minimum thresholds for production
          const requiredThresholds = {
            branches: 85,
            functions: 90,
            lines: 90,
            statements: 90
          };
          
          Object.entries(requiredThresholds).forEach(([metric, minValue]) => {
            const metricMatch = thresholds.match(new RegExp(`${metric}:\\s*(\\d+)`));
            if (metricMatch) {
              const currentValue = parseInt(metricMatch[1]);
              if (currentValue < minValue) {
                coverageIssues.push(`${metric} threshold ${currentValue}% too low (should be ≥${minValue}%)`);
              }
            } else {
              coverageIssues.push(`Missing ${metric} coverage threshold`);
            }
          });
        } else {
          coverageIssues.push('No global coverage thresholds configured');
        }
        
        // Check for per-directory thresholds
        const hasPerDirectoryThresholds = configContent.includes('coverageThreshold') && 
                                        (configContent.includes('src/') || configContent.includes('./'));
        
        if (!hasPerDirectoryThresholds) {
          coverageIssues.push('Missing per-directory coverage thresholds');
        }
        
        // EXPECTED TO FAIL: Coverage thresholds should be adequate for production
        expect(coverageIssues.length).toBe(0);
      } else {
        // EXPECTED TO FAIL: Jest config should exist
        expect(fs.existsSync(jestConfigPath)).toBe(true);
      }
    });

    it('should fail - coverage configuration lacks critical file exclusions', () => {
      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');
      
      if (fs.existsSync(jestConfigPath)) {
        const configContent = fs.readFileSync(jestConfigPath, 'utf8');
        const exclusionIssues = [];
        
        // Check for coverage exclusions
        const coveragePathIgnorePatternsMatch = configContent.match(/coveragePathIgnorePatterns:\s*\[([^\]]+)\]/s);
        
        if (coveragePathIgnorePatternsMatch) {
          const exclusions = coveragePathIgnorePatternsMatch[1];
          
          // Required exclusions
          const requiredExclusions = [
            'node_modules',
            '__tests__',
            'coverage',
            '.next',
            'dist',
            'build'
          ];
          
          requiredExclusions.forEach(exclusion => {
            if (!exclusions.includes(exclusion)) {
              exclusionIssues.push(`Missing coverage exclusion: ${exclusion}`);
            }
          });
        } else {
          exclusionIssues.push('No coveragePathIgnorePatterns configured');
        }
        
        // Check for collectCoverageFrom configuration
        const collectCoverageFromMatch = configContent.match(/collectCoverageFrom:\s*\[([^\]]+)\]/s);
        
        if (collectCoverageFromMatch) {
          const collectFrom = collectCoverageFromMatch[1];
          
          // Should include source files
          if (!collectFrom.includes('src/') && !collectFrom.includes('**/*.{ts,tsx}')) {
            exclusionIssues.push('collectCoverageFrom missing source file patterns');
          }
          
          // Should exclude test files
          if (!collectFrom.includes('!**/*.test.*') && !collectFrom.includes('!**/__tests__/**')) {
            exclusionIssues.push('collectCoverageFrom not excluding test files');
          }
        } else {
          exclusionIssues.push('No collectCoverageFrom configuration');
        }
        
        // EXPECTED TO FAIL: Coverage configuration should have proper exclusions
        expect(exclusionIssues.length).toBe(0);
      }
    });
  });

  describe('CRITICAL ISSUE 3: Missing CI/CD Automation', () => {
    it('should fail - GitHub Actions workflow is missing or incomplete', () => {
      const workflowsDir = path.join(process.cwd(), '.github/workflows');
      const automationIssues = [];
      
      if (fs.existsSync(workflowsDir)) {
        const workflowFiles = fs.readdirSync(workflowsDir).filter(file => 
          file.endsWith('.yml') || file.endsWith('.yaml')
        );
        
        if (workflowFiles.length === 0) {
          automationIssues.push('No GitHub Actions workflow files found');
        } else {
          // Check for required workflows
          const requiredWorkflows = ['ci', 'test', 'build'];
          const hasRequiredWorkflow = requiredWorkflows.some(workflow => 
            workflowFiles.some(file => file.toLowerCase().includes(workflow))
          );
          
          if (!hasRequiredWorkflow) {
            automationIssues.push('Missing required CI/test/build workflow');
          }
          
          // Analyze workflow content
          workflowFiles.forEach(workflowFile => {
            const workflowPath = path.join(workflowsDir, workflowFile);
            const workflowContent = fs.readFileSync(workflowPath, 'utf8');
            
            // Check for required workflow components
            const requiredComponents = [
              'on:', // Trigger events
              'jobs:', // Job definitions
              'runs-on:', // Runner specification
              'uses: actions/checkout', // Code checkout
              'uses: actions/setup-node', // Node.js setup
              'npm ci', // Dependency installation
              'npm test' // Test execution
            ];
            
            requiredComponents.forEach(component => {
              if (!workflowContent.includes(component)) {
                automationIssues.push(`${workflowFile}: Missing ${component}`);
              }
            });
            
            // Check for test result reporting
            if (!workflowContent.includes('coverage') && !workflowContent.includes('test-results')) {
              automationIssues.push(`${workflowFile}: Missing test result reporting`);
            }
          });
        }
      } else {
        automationIssues.push('No .github/workflows directory found');
      }
      
      // EXPECTED TO FAIL: GitHub Actions automation should be properly configured
      expect(automationIssues.length).toBe(0);
    });

    it('should fail - pre-commit hooks and quality gates are missing', () => {
      const qualityGateIssues = [];
      
      // Check for pre-commit hooks
      const huskyDir = path.join(process.cwd(), '.husky');
      const preCommitHook = path.join(huskyDir, 'pre-commit');
      
      if (!fs.existsSync(huskyDir)) {
        qualityGateIssues.push('No .husky directory found (missing pre-commit hooks)');
      } else if (!fs.existsSync(preCommitHook)) {
        qualityGateIssues.push('No pre-commit hook configured');
      } else {
        const hookContent = fs.readFileSync(preCommitHook, 'utf8');
        
        // Pre-commit should run tests
        if (!hookContent.includes('test') && !hookContent.includes('lint')) {
          qualityGateIssues.push('Pre-commit hook missing test/lint execution');
        }
      }
      
      // Check for lint-staged configuration
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        if (!packageJson['lint-staged']) {
          qualityGateIssues.push('No lint-staged configuration found');
        }
      }
      
      // Check for commitlint configuration
      const commitlintConfigs = [
        'commitlint.config.js',
        '.commitlintrc.js',
        '.commitlintrc.json'
      ];
      
      const hasCommitlintConfig = commitlintConfigs.some(config => 
        fs.existsSync(path.join(process.cwd(), config))
      );
      
      if (!hasCommitlintConfig) {
        qualityGateIssues.push('No commitlint configuration found');
      }
      
      // EXPECTED TO FAIL: Quality gates should be properly configured
      expect(qualityGateIssues.length).toBe(0);
    });
  });

  describe('CRITICAL ISSUE 4: Pipeline Performance and Reliability Issues', () => {
    it('should fail - CI pipeline lacks caching and optimization', () => {
      const workflowsDir = path.join(process.cwd(), '.github/workflows');
      const performanceIssues = [];
      
      if (fs.existsSync(workflowsDir)) {
        const workflowFiles = fs.readdirSync(workflowsDir).filter(file => 
          file.endsWith('.yml') || file.endsWith('.yaml')
        );
        
        workflowFiles.forEach(workflowFile => {
          const workflowPath = path.join(workflowsDir, workflowFile);
          const workflowContent = fs.readFileSync(workflowPath, 'utf8');
          
          // Check for caching
          if (!workflowContent.includes('actions/cache') && !workflowContent.includes('cache:')) {
            performanceIssues.push(`${workflowFile}: Missing dependency caching`);
          }
          
          // Check for parallel job execution
          if (workflowContent.includes('npm test') && !workflowContent.includes('strategy:')) {
            performanceIssues.push(`${workflowFile}: Missing parallel execution strategy`);
          }
          
          // Check for artifact management
          if (!workflowContent.includes('actions/upload-artifact') && workflowContent.includes('coverage')) {
            performanceIssues.push(`${workflowFile}: Missing artifact upload for coverage`);
          }
        });
      }
      
      // Check Jest configuration for CI optimization
      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');
      if (fs.existsSync(jestConfigPath)) {
        const configContent = fs.readFileSync(jestConfigPath, 'utf8');
        
        // Should have maxWorkers configuration for CI
        if (!configContent.includes('maxWorkers')) {
          performanceIssues.push('Jest config missing maxWorkers optimization for CI');
        }
        
        // Should have cache configuration
        if (!configContent.includes('cache') && !configContent.includes('cacheDirectory')) {
          performanceIssues.push('Jest config missing cache configuration');
        }
      }
      
      // EXPECTED TO FAIL: CI pipeline should be optimized for performance
      expect(performanceIssues.length).toBe(0);
    });

    it('should fail - pipeline lacks proper error handling and retry mechanisms', () => {
      const workflowsDir = path.join(process.cwd(), '.github/workflows');
      const reliabilityIssues = [];
      
      if (fs.existsSync(workflowsDir)) {
        const workflowFiles = fs.readdirSync(workflowsDir).filter(file => 
          file.endsWith('.yml') || file.endsWith('.yaml')
        );
        
        workflowFiles.forEach(workflowFile => {
          const workflowPath = path.join(workflowsDir, workflowFile);
          const workflowContent = fs.readFileSync(workflowPath, 'utf8');
          
          // Check for timeout configuration
          if (!workflowContent.includes('timeout-minutes:')) {
            reliabilityIssues.push(`${workflowFile}: Missing job timeout configuration`);
          }
          
          // Check for retry mechanisms
          if (!workflowContent.includes('continue-on-error') && !workflowContent.includes('if: failure()')) {
            reliabilityIssues.push(`${workflowFile}: Missing error handling mechanisms`);
          }
          
          // Check for notification on failure
          if (!workflowContent.includes('slack') && !workflowContent.includes('notification')) {
            reliabilityIssues.push(`${workflowFile}: Missing failure notification`);
          }
          
          // Check for matrix strategy reliability
          if (workflowContent.includes('matrix:') && !workflowContent.includes('fail-fast: false')) {
            reliabilityIssues.push(`${workflowFile}: Matrix strategy should not fail fast`);
          }
        });
      }
      
      // EXPECTED TO FAIL: Pipeline should have proper error handling
      expect(reliabilityIssues.length).toBe(0);
    });
  });

  describe('CRITICAL ISSUE 5: Test Reporting and Monitoring Gaps', () => {
    it('should fail - test results lack comprehensive reporting and analytics', () => {
      const reportingIssues = [];
      
      // Check Jest configuration for reporting
      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');
      if (fs.existsSync(jestConfigPath)) {
        const configContent = fs.readFileSync(jestConfigPath, 'utf8');
        
        // Check for reporters configuration
        const reportersMatch = configContent.match(/reporters:\s*\[([^\]]+)\]/s);
        if (reportersMatch) {
          const reporters = reportersMatch[1];
          
          // Should have multiple reporters for different purposes
          const requiredReporters = ['default', 'json', 'html'];
          requiredReporters.forEach(reporter => {
            if (!reporters.includes(reporter)) {
              reportingIssues.push(`Missing ${reporter} reporter in Jest configuration`);
            }
          });
        } else {
          reportingIssues.push('No reporters configuration in Jest config');
        }
        
        // Check for coverage reporters
        const coverageReportersMatch = configContent.match(/coverageReporters:\s*\[([^\]]+)\]/s);
        if (coverageReportersMatch) {
          const coverageReporters = coverageReportersMatch[1];
          
          const requiredCoverageReporters = ['text', 'lcov', 'html', 'json'];
          requiredCoverageReporters.forEach(reporter => {
            if (!coverageReporters.includes(reporter)) {
              reportingIssues.push(`Missing ${reporter} coverage reporter`);
            }
          });
        } else {
          reportingIssues.push('No coverage reporters configuration');
        }
      }
      
      // Check for test result directories
      const expectedReportDirs = ['coverage', 'test-results', 'reports'];
      expectedReportDirs.forEach(dir => {
        const dirPath = path.join(process.cwd(), dir);
        if (!fs.existsSync(dirPath)) {
          reportingIssues.push(`Missing ${dir} directory for test reports`);
        }
      });
      
      // EXPECTED TO FAIL: Test reporting should be comprehensive
      expect(reportingIssues.length).toBe(0);
    });

    it('should fail - monitoring and alerting for test failures is inadequate', () => {
      const monitoringIssues = [];
      
      // Check for monitoring configuration
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        // Check for monitoring dependencies
        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
        const monitoringTools = ['@sentry/node', 'datadog', 'newrelic'];
        
        const hasMonitoring = monitoringTools.some(tool => dependencies[tool]);
        if (!hasMonitoring) {
          monitoringIssues.push('No monitoring tools configured for test failure tracking');
        }
      }
      
      // Check GitHub Actions for monitoring integration
      const workflowsDir = path.join(process.cwd(), '.github/workflows');
      if (fs.existsSync(workflowsDir)) {
        const workflowFiles = fs.readdirSync(workflowsDir).filter(file => 
          file.endsWith('.yml') || file.endsWith('.yaml')
        );
        
        let hasFailureNotification = false;
        workflowFiles.forEach(workflowFile => {
          const workflowPath = path.join(workflowsDir, workflowFile);
          const workflowContent = fs.readFileSync(workflowPath, 'utf8');
          
          if (workflowContent.includes('if: failure()') || workflowContent.includes('slack')) {
            hasFailureNotification = true;
          }
        });
        
        if (!hasFailureNotification) {
          monitoringIssues.push('No failure notification configured in CI workflows');
        }
      }
      
      // EXPECTED TO FAIL: Monitoring and alerting should be properly configured
      expect(monitoringIssues.length).toBe(0);
    });
  });
});
