/**
 * Test Environment Configuration Issues Tests
 * 
 * These tests prove test environment issues including inconsistent environment
 * variables, unsafe database configurations, and missing cleanup hooks.
 * 
 * EXPECTED TO FAIL - These tests demonstrate environment configuration issues that need fixing.
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import fs from 'fs';
import path from 'path';

describe('Test Environment Configuration Issues', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('CRITICAL ISSUE 1: Inconsistent Environment Variables', () => {
    it('should fail - test environment variables are inconsistent across setup files', () => {
      // Check jest.setup.js for proper environment configuration
      const setupFiles = [
        'jest.setup.js',
        'jest.setup.integration.js',
        'jest.polyfills.js'
      ];
      
      const envVarIssues = [];
      
      setupFiles.forEach(setupFile => {
        const setupPath = path.join(process.cwd(), setupFile);
        
        if (fs.existsSync(setupPath)) {
          const setupContent = fs.readFileSync(setupPath, 'utf8');
          
          // Required environment variables for testing
          const requiredEnvVars = [
            'NEXTAUTH_URL',
            'NEXTAUTH_SECRET',
            'DATABASE_URL',
            'NODE_ENV'
          ];
          
          const missingEnvVars = requiredEnvVars.filter(envVar => 
            !setupContent.includes(`process.env.${envVar}`)
          );
          
          if (missingEnvVars.length > 0) {
            envVarIssues.push({ file: setupFile, missingEnvVars });
          }
          
          // Check for hardcoded test values that might cause issues
          const hasHardcodedSecrets = setupContent.includes('test-secret') || 
                                     setupContent.includes('localhost:5432');
          
          if (hasHardcodedSecrets) {
            envVarIssues.push({ file: setupFile, issue: 'Contains hardcoded secrets' });
          }
        } else {
          envVarIssues.push({ file: setupFile, issue: 'Setup file does not exist' });
        }
      });
      
      // EXPECTED TO FAIL: All setup files should have consistent environment configuration
      expect(envVarIssues.length).toBe(0);
    });

    it('should fail - environment variables have different values across test configurations', () => {
      // Check for environment variable consistency
      const configFiles = [
        'jest.config.js',
        'jest.setup.js',
        'jest.setup.integration.js',
        '.env.test',
        '.env.local'
      ];
      
      const envValues = {};
      const inconsistencies = [];
      
      configFiles.forEach(configFile => {
        const configPath = path.join(process.cwd(), configFile);
        
        if (fs.existsSync(configPath)) {
          const content = fs.readFileSync(configPath, 'utf8');
          
          // Extract environment variable assignments
          const envMatches = content.match(/process\.env\.(\w+)\s*=\s*['"`]([^'"`]+)['"`]/g) || [];
          
          envMatches.forEach(match => {
            const [, varName, value] = match.match(/process\.env\.(\w+)\s*=\s*['"`]([^'"`]+)['"`]/);
            
            if (!envValues[varName]) {
              envValues[varName] = [];
            }
            envValues[varName].push({ file: configFile, value });
          });
        }
      });
      
      // Check for inconsistent values
      Object.entries(envValues).forEach(([varName, assignments]) => {
        const assignmentArray = assignments as any[];
        if (assignmentArray.length > 1) {
          const uniqueValues = [...new Set(assignmentArray.map((a: any) => a.value))];
          if (uniqueValues.length > 1) {
            inconsistencies.push({ variable: varName, assignments: assignmentArray });
          }
        }
      });
      
      // EXPECTED TO FAIL: Environment variables should have consistent values
      expect(inconsistencies.length).toBe(0);
    });
  });

  describe('CRITICAL ISSUE 2: Unsafe Database Configuration', () => {
    it('should fail - test database configuration is unsafe for production', () => {
      // Check for proper test database isolation
      const setupFiles = [
        'jest.setup.js',
        'jest.setup.integration.js'
      ];
      
      const unsafeConfigurations = [];
      
      setupFiles.forEach(setupFile => {
        const setupPath = path.join(process.cwd(), setupFile);
        
        if (fs.existsSync(setupPath)) {
          const content = fs.readFileSync(setupPath, 'utf8');
          
          // Check for unsafe database configurations
          if (content.includes('DATABASE_URL') && !content.includes('test')) {
            unsafeConfigurations.push(`${setupFile}: DATABASE_URL doesn't include 'test'`);
          }
          
          // Check for production database references
          if (content.includes('production') || content.includes('prod')) {
            unsafeConfigurations.push(`${setupFile}: Contains production references`);
          }
          
          // Check for real database URLs
          const dbUrlMatches = content.match(/postgresql:\/\/[^'"`\s]+/g) || [];
          dbUrlMatches.forEach(url => {
            if (!url.includes('test') && !url.includes('localhost')) {
              unsafeConfigurations.push(`${setupFile}: Real database URL detected: ${url}`);
            }
          });
        }
      });
      
      // EXPECTED TO FAIL: Test database configuration should be safe
      expect(unsafeConfigurations.length).toBe(0);
    });

    it('should fail - database cleanup hooks are missing or inadequate', () => {
      // Check for database cleanup in test files
      const testDirectory = path.join(process.cwd(), '__tests__');
      const missingCleanupTests = [];
      
      function checkDatabaseCleanup(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            checkDatabaseCleanup(filePath);
          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
            try {
              const content = fs.readFileSync(filePath, 'utf8');
              
              // Check if test uses database operations
              const hasDbOperations = content.includes('prisma') || 
                                    content.includes('database') ||
                                    content.includes('PrismaClient');
              
              if (hasDbOperations) {
                // Check for cleanup hooks
                const hasBeforeEach = content.includes('beforeEach');
                const hasAfterEach = content.includes('afterEach');
                const hasCleanup = content.includes('cleanup') || 
                                 content.includes('clear') || 
                                 content.includes('reset') ||
                                 content.includes('truncate');
                
                if (!hasAfterEach && !hasCleanup) {
                  missingCleanupTests.push({ 
                    file: filePath, 
                    hasBeforeEach, 
                    hasAfterEach, 
                    hasCleanup 
                  });
                }
              }
            } catch (error) {
              // Skip files that can't be read
            }
          }
        });
      }
      
      checkDatabaseCleanup(testDirectory);
      
      // EXPECTED TO FAIL: Database tests should have proper cleanup
      expect(missingCleanupTests.length).toBe(0);
    });
  });

  describe('CRITICAL ISSUE 3: Test Isolation Problems', () => {
    it('should fail - tests share state and interfere with each other', () => {
      // Check for global state sharing issues
      const testDirectory = path.join(process.cwd(), '__tests__');
      const stateInterferenceIssues = [];
      
      function checkStateIsolation(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            checkStateIsolation(filePath);
          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
            try {
              const content = fs.readFileSync(filePath, 'utf8');
              
              // Check for global variable usage
              const globalVarUsage = (content.match(/global\.|window\.|process\.env\./g) || []).length;
              const hasStateReset = content.includes('beforeEach') || 
                                  content.includes('afterEach') ||
                                  content.includes('jest.clearAllMocks');
              
              // Check for shared test data
              const hasSharedData = content.includes('let ') && 
                                  !content.includes('beforeEach') &&
                                  (content.match(/it\(/g) || []).length > 1;
              
              if ((globalVarUsage > 5 && !hasStateReset) || hasSharedData) {
                stateInterferenceIssues.push({ 
                  file: filePath, 
                  globalVarUsage, 
                  hasStateReset, 
                  hasSharedData 
                });
              }
            } catch (error) {
              // Skip files that can't be read
            }
          }
        });
      }
      
      checkStateIsolation(testDirectory);
      
      // EXPECTED TO FAIL: Tests should not share state
      expect(stateInterferenceIssues.length).toBe(0);
    });

    it('should fail - mock configurations leak between tests', () => {
      // Check for mock leakage issues
      const testDirectory = path.join(process.cwd(), '__tests__');
      const mockLeakageIssues = [];
      
      function checkMockLeakage(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            checkMockLeakage(filePath);
          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
            try {
              const content = fs.readFileSync(filePath, 'utf8');
              
              const mockCount = (content.match(/jest\.mock|mockImplementation|mockReturnValue/g) || []).length;
              const hasMockClear = content.includes('clearAllMocks') || 
                                 content.includes('resetAllMocks') ||
                                 content.includes('restoreAllMocks');
              const testCount = (content.match(/it\(/g) || []).length;
              
              // If there are multiple tests with mocks but no mock clearing
              if (mockCount > 3 && testCount > 1 && !hasMockClear) {
                mockLeakageIssues.push({ 
                  file: filePath, 
                  mockCount, 
                  testCount, 
                  hasMockClear 
                });
              }
            } catch (error) {
              // Skip files that can't be read
            }
          }
        });
      }
      
      checkMockLeakage(testDirectory);
      
      // EXPECTED TO FAIL: Mocks should not leak between tests
      expect(mockLeakageIssues.length).toBe(0);
    });
  });

  describe('CRITICAL ISSUE 4: Test Configuration Inconsistencies', () => {
    it('should fail - Jest configuration has conflicting settings', () => {
      // Check Jest configuration for conflicts
      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');
      
      if (fs.existsSync(jestConfigPath)) {
        const configContent = fs.readFileSync(jestConfigPath, 'utf8');
        const configIssues = [];
        
        // Check for conflicting test environments
        const testEnvMatches = configContent.match(/testEnvironment:\s*['"`]([^'"`]+)['"`]/);
        const testEnv = testEnvMatches ? testEnvMatches[1] : 'node';
        
        // Check for DOM-related tests with node environment
        if (testEnv === 'node' && configContent.includes('jsdom')) {
          configIssues.push('Conflicting test environment: node vs jsdom');
        }
        
        // Check for module name mapping conflicts
        const moduleMapMatches = configContent.match(/moduleNameMapper:\s*{([^}]+)}/s);
        if (moduleMapMatches) {
          const moduleMap = moduleMapMatches[1];
          const mappings = moduleMap.split(',');
          
          // Check for conflicting path mappings
          const pathMappings = {};
          mappings.forEach(mapping => {
            const match = mapping.match(/['"`]([^'"`]+)['"`]:\s*['"`]([^'"`]+)['"`]/);
            if (match) {
              const [, pattern, replacement] = match;
              if (pathMappings[pattern] && pathMappings[pattern] !== replacement) {
                configIssues.push(`Conflicting module mapping for ${pattern}`);
              }
              pathMappings[pattern] = replacement;
            }
          });
        }
        
        // Check for coverage threshold conflicts
        const coverageMatches = configContent.match(/coverageThreshold:\s*{[^}]+global:\s*{([^}]+)}/s);
        if (coverageMatches) {
          const thresholds = coverageMatches[1];
          const branchesMatch = thresholds.match(/branches:\s*(\d+)/);
          const linesMatch = thresholds.match(/lines:\s*(\d+)/);
          
          if (branchesMatch && linesMatch) {
            const branches = parseInt(branchesMatch[1]);
            const lines = parseInt(linesMatch[1]);
            
            // Branches threshold should not be higher than lines threshold
            if (branches > lines) {
              configIssues.push('Branch coverage threshold higher than line coverage');
            }
          }
        }
        
        // EXPECTED TO FAIL: Jest configuration should not have conflicts
        expect(configIssues.length).toBe(0);
      } else {
        // EXPECTED TO FAIL: Jest config should exist
        expect(fs.existsSync(jestConfigPath)).toBe(true);
      }
    });

    it('should fail - test scripts in package.json are incomplete or conflicting', () => {
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        const scripts = packageJson.scripts || {};
        const scriptIssues = [];
        
        // Check for required test scripts
        const requiredTestScripts = [
          'test',
          'test:coverage',
          'test:watch',
          'test:ci'
        ];
        
        const missingScripts = requiredTestScripts.filter(script => !scripts[script]);
        if (missingScripts.length > 0) {
          scriptIssues.push(`Missing required scripts: ${missingScripts.join(', ')}`);
        }
        
        // Check for conflicting test configurations
        const testScript = scripts['test'];
        const testCiScript = scripts['test:ci'];
        
        if (testScript && testCiScript) {
          // CI script should disable watch mode
          if (testCiScript.includes('--watch') && !testCiScript.includes('--watchAll=false')) {
            scriptIssues.push('CI test script should disable watch mode');
          }
          
          // CI script should include coverage
          if (!testCiScript.includes('--coverage')) {
            scriptIssues.push('CI test script should include coverage reporting');
          }
        }
        
        // EXPECTED TO FAIL: Package.json test scripts should be complete and consistent
        expect(scriptIssues.length).toBe(0);
      } else {
        // EXPECTED TO FAIL: package.json should exist
        expect(fs.existsSync(packageJsonPath)).toBe(true);
      }
    });
  });

  describe('CRITICAL ISSUE 5: External Service Configuration Problems', () => {
    it('should fail - external service mocks are not properly configured for testing', () => {
      // Check for external service mock configuration
      const setupFiles = [
        'jest.setup.js',
        'jest.setup.integration.js',
        '__mocks__'
      ];
      
      const externalServiceIssues = [];
      
      // External services that should be mocked in tests
      const externalServices = [
        'fetch',
        'gemini',
        'openai',
        'resend',
        'sentry'
      ];
      
      setupFiles.forEach(setupFile => {
        const setupPath = path.join(process.cwd(), setupFile);
        
        if (fs.existsSync(setupPath)) {
          const isDirectory = fs.statSync(setupPath).isDirectory();
          
          if (isDirectory) {
            // Check __mocks__ directory
            const mockFiles = fs.readdirSync(setupPath);
            const mockedServices = mockFiles.map(file => file.replace(/\.(js|ts)$/, ''));
            
            const unmockedServices = externalServices.filter(service => 
              !mockedServices.includes(service)
            );
            
            if (unmockedServices.length > 2) {
              externalServiceIssues.push({ 
                location: setupFile, 
                issue: `Unmocked external services: ${unmockedServices.join(', ')}` 
              });
            }
          } else {
            // Check setup file content
            const content = fs.readFileSync(setupPath, 'utf8');
            
            const mockedInSetup = externalServices.filter(service => 
              content.includes(`jest.mock`) && content.includes(service)
            );
            
            // Should mock critical external services
            if (mockedInSetup.length < 2) {
              externalServiceIssues.push({ 
                location: setupFile, 
                issue: 'Insufficient external service mocking' 
              });
            }
          }
        }
      });
      
      // EXPECTED TO FAIL: External services should be properly mocked
      expect(externalServiceIssues.length).toBe(0);
    });
  });
});
